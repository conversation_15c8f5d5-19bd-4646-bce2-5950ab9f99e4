<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Truly Painting - Professional Painting Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#D4A59A", // rose gold as main color for primary CTAs
              secondary: "#2A2A2A", // deep charcoal/off-black as secondary color
              tertiary: "#1E3A8A", // deep blue for tertiary elements
              dark: "#1A1A1A", // dark color for dark mode sections
              light: "#F8F8F8", // light color for light sections
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .hover-grow {
        transition: transform 0.2s ease;
      }
      .hover-grow:hover {
        transform: scale(1.02);
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
      }

      /* Custom rose gold gradient for headlines */
      .rose-gold-gradient {
        background: linear-gradient(
          to right,
          #d4a59a,
          /* Base rose gold */ #e8c3b9,
          /* Lighter rose gold */ #c69489,
          /* Deeper rose gold */ #d4a59a /* Back to base rose gold */
        );
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        text-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);
      }
    </style>
    
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-NBKXB3TF");
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      id="header"
      class="fixed w-full z-50 transition-all duration-300 py-4"
    >
      <div class="container mx-auto px-4 md:px-6">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <a href="index.html">
              <img
                src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
                alt="Truly Painting Logo"
                class="h-12 logo-transparent"
              />
              <img
                src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
                alt="Truly Painting Logo"
                class="h-12 logo-solid hidden"
              />
            </a>
          </div>

          <div class="hidden md:flex items-center space-x-8">
            <nav>
              <ul class="flex space-x-6">
                <li>
                  <a
                    href="services.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Services</a
                  >
                </li>

                <li>
                  <a
                    href="team.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Our Team</a
                  >
                </li>
                <li>
                  <a
                    href="visualizer.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Visualizer</a
                  >
                </li>
                <li>
                  <a
                    href="#contact"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Contact</a
                  >
                </li>
              </ul>
            </nav>

            <a
              href="tel:5094617090"
              class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md font-medium transition"
            >
              <svg
                class="w-[18px] h-[18px] mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                />
              </svg>
              (*************
            </a>
          </div>

          <button class="md:hidden text-white" onclick="toggleMobileMenu()">
            <svg
              class="w-7 h-7"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobileMenu"
        class="hidden md:hidden bg-white shadow-lg fixed top-0 left-0 right-0 z-50 mt-16 max-h-[calc(100vh-4rem)] overflow-y-auto"
      >
        <nav class="container mx-auto px-4 py-4">
          <ul class="space-y-4">
            <li>
              <a
                href="services.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>

            <li>
              <a
                href="team.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="visualizer.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Visualizer
              </a>
            </li>
            <li>
              <a
                href="#contact"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Contact
              </a>
            </li>
            <li>
              <a
                href="tel:5094617090"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-medium justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                (*************
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <script>
      // Header scroll effect
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navLinks = document.querySelectorAll(".nav-link");
        const logoTransparent = document.querySelector(".logo-transparent");
        const logoSolid = document.querySelector(".logo-solid");

        if (window.scrollY > 20) {
          header.classList.remove("py-4");
          header.classList.add("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.remove("text-white");
            link.classList.add("text-gray-800");
          });
          logoTransparent.classList.add("hidden");
          logoSolid.classList.remove("hidden");
        } else {
          header.classList.add("py-4");
          header.classList.remove("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.add("text-white");
            link.classList.remove("text-gray-800");
          });
          logoTransparent.classList.remove("hidden");
          logoSolid.classList.add("hidden");
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const header = document.getElementById("header");

        // Adjust mobile menu position based on header height
        const headerHeight = header.offsetHeight;
        mobileMenu.style.marginTop = headerHeight + "px";

        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }
    </script>

    <!-- Hero Section -->
    <section
      class="relative min-h-[70vh] md:h-[55vh] lg:h-[50vh] flex items-center py-8 md:py-0"
    >
      <div class="absolute inset-0">
        <img
          src="https://images.leadconnectorhq.com/image/f_webp/q_80/r_1500/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f9a0ad363e9275d5c.webp"
          alt="Beautiful Painting Project by Truly Painting"
          class="w-full h-full object-cover"
        />
        <div class="absolute inset-0 bg-black opacity-60"></div>
      </div>

      <div class="container mx-auto px-4 relative z-10">
        <div class="grid md:grid-cols-5 gap-8 items-center">
          <div class="md:col-span-3 text-white">
            <h1
              class="text-3xl md:text-5xl font-bold leading-tight mb-4 md:mb-6 animate-fadeIn"
            >
              <span class="rose-gold-gradient"
                >Spokane, See Your Home Transformed</span
              >
            </h1>
            <p class="hidden md:block text-xl mb-8 opacity-90">
              Don't Just Imagine Your New Paint Job – See It! Try Our Free AI
              Paint Preview, Then Let Us Bring It to Life with Expert
              Craftsmanship.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 mb-8 md:mb-0">
              <a
                href="#contact"
                class="px-6 md:px-8 py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-md text-center transition"
              >
                Get Your FREE Estimate Today!
              </a>
              <a
                href="tel:5094617090"
                class="px-6 md:px-8 py-3 bg-white hover:bg-gray-100 text-primary font-semibold rounded-md text-center transition flex items-center justify-center"
              >
                <svg
                  class="w-5 h-5 mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Call Now: (*************
              </a>
            </div>
          </div>

          <div class="md:col-span-2 w-full">
            <div class="bg-white rounded-xl shadow-xl max-w-sm mx-auto w-full">
              <!-- JobTread Form -->
              <style>
                @layer properties;
                @layer theme, base, components, utilities;
                @layer theme {
                  .jtwf {
                    --color-red-500: oklch(63.7% 0.237 25.331);
                    --color-blue-500: oklch(62.3% 0.214 259.815);
                    --color-gray-50: oklch(98.5% 0.002 247.839);
                    --color-gray-200: oklch(92.8% 0.006 264.531);
                    --color-gray-300: oklch(87.2% 0.01 258.338);
                    --color-gray-500: oklch(55.1% 0.027 264.364);
                    --color-white: #fff;
                    --spacing: 0.25rem;
                    --container-sm: 24rem;
                    --text-xl: 1.25rem;
                    --text-xl--line-height: 1.4;
                    --font-weight-normal: 400;
                    --font-weight-bold: 700;
                    --radius-sm: 0.25rem;
                    --default-transition-duration: 250ms;
                    --default-transition-timing-function: cubic-bezier(
                      0.4,
                      0,
                      0.2,
                      1
                    );
                  }
                }
                @layer utilities {
                  .jtwf .mx-auto {
                    margin-inline: auto;
                  }
                  .jtwf .block {
                    display: block;
                  }
                  .jtwf .flex {
                    display: flex;
                  }
                  .jtwf .max-h-24 {
                    max-height: calc(var(--spacing) * 24);
                  }
                  .jtwf .w-full {
                    width: 100%;
                  }
                  .jtwf .max-w-full {
                    max-width: 100%;
                  }
                  .jtwf .max-w-sm {
                    max-width: var(--container-sm);
                  }
                  .jtwf .min-w-0 {
                    min-width: calc(var(--spacing) * 0);
                  }
                  .jtwf .shrink-0 {
                    flex-shrink: 0;
                  }
                  .jtwf .grow {
                    flex-grow: 1;
                  }
                  .jtwf .cursor-pointer {
                    cursor: pointer;
                  }
                  .jtwf .appearance-none {
                    appearance: none;
                  }
                  .jtwf .items-center {
                    align-items: center;
                  }
                  .jtwf .gap-2 {
                    gap: calc(var(--spacing) * 2);
                  }
                  .jtwf .space-y-2 {
                    :where(& > :not(:last-child)) {
                      --tw-space-y-reverse: 0;
                      margin-block-end: calc(
                        var(--spacing) * 2 * (1 - var(--tw-space-y-reverse))
                      );
                      margin-block-start: calc(
                        var(--spacing) * 2 * var(--tw-space-y-reverse)
                      );
                    }
                  }
                  .jtwf .divide-y {
                    :where(& > :not(:last-child)) {
                      --tw-divide-y-reverse: 0;
                      border-bottom-style: var(--tw-border-style);
                      border-bottom-width: calc(
                        1px * (1 - var(--tw-divide-y-reverse))
                      );
                      border-top-style: var(--tw-border-style);
                      border-top-width: calc(1px * var(--tw-divide-y-reverse));
                    }
                  }
                  .jtwf .overflow-hidden {
                    overflow: hidden;
                  }
                  .jtwf .rounded-sm {
                    border-radius: var(--radius-sm);
                  }
                  .jtwf .border {
                    border-style: var(--tw-border-style);
                    border-width: 1px;
                  }
                  .jtwf .bg-white {
                    background-color: var(--color-white);
                  }
                  .jtwf .p-2 {
                    padding: calc(var(--spacing) * 2);
                  }
                  .jtwf .p-4 {
                    padding: calc(var(--spacing) * 4);
                  }
                  .jtwf .px-4 {
                    padding-inline: calc(var(--spacing) * 4);
                  }
                  .jtwf .py-2 {
                    padding-block: calc(var(--spacing) * 2);
                  }
                  .jtwf .text-center {
                    text-align: center;
                  }
                  .jtwf .text-right {
                    text-align: right;
                  }
                  .jtwf .text-xl {
                    font-size: var(--text-xl);
                    line-height: var(--tw-leading, var(--text-xl--line-height));
                  }
                  .jtwf .font-bold {
                    --tw-font-weight: var(--font-weight-bold);
                    font-weight: var(--font-weight-bold);
                  }
                  .jtwf .font-normal {
                    --tw-font-weight: var(--font-weight-normal);
                    font-weight: var(--font-weight-normal);
                  }
                  .jtwf .text-gray-500 {
                    color: var(--color-gray-500);
                  }
                  .jtwf .text-red-500 {
                    color: var(--color-red-500);
                  }
                  .jtwf .shadow-sm {
                    --tw-shadow: 0 1px 3px 0
                        var(--tw-shadow-color, rgba(0, 0, 0, 0.1)),
                      0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, 0.1));
                  }
                  .jtwf .shadow-sm,
                  .jtwf .shadow-xs {
                    box-shadow: var(--tw-inset-shadow),
                      var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow),
                      var(--tw-ring-shadow), var(--tw-shadow);
                  }
                  .jtwf .shadow-xs {
                    --tw-shadow: 0 1px 2px 0
                      var(--tw-shadow-color, rgba(0, 0, 0, 0.05));
                  }
                  .jtwf .ring {
                    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
                      calc(1px + var(--tw-ring-offset-width))
                      var(--tw-ring-color, currentcolor);
                    box-shadow: var(--tw-inset-shadow),
                      var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow),
                      var(--tw-ring-shadow), var(--tw-shadow);
                  }
                  .jtwf .ring-gray-200 {
                    --tw-ring-color: var(--color-gray-200);
                  }
                  .jtwf .transition {
                    transition-duration: var(
                      --tw-duration,
                      var(--default-transition-duration)
                    );
                    transition-property: color, background-color, border-color,
                      outline-color, text-decoration-color, fill, stroke,
                      --tw-gradient-from, --tw-gradient-via, --tw-gradient-to,
                      opacity, box-shadow, transform, translate, scale, rotate,
                      filter, -webkit-backdrop-filter, backdrop-filter, display,
                      visibility, content-visibility, overlay, pointer-events;
                    transition-timing-function: var(
                      --tw-ease,
                      var(--default-transition-timing-function)
                    );
                  }
                  .jtwf .focus-within\:bg-white {
                    &:focus-within {
                      background-color: var(--color-white);
                    }
                  }
                  .jtwf .focus-within\:ring-3 {
                    &:focus-within {
                      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
                        calc(3px + var(--tw-ring-offset-width))
                        var(--tw-ring-color, currentcolor);
                      box-shadow: var(--tw-inset-shadow),
                        var(--tw-inset-ring-shadow),
                        var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
                        var(--tw-shadow);
                    }
                  }
                  .jtwf .focus-within\:ring-blue-500 {
                    &:focus-within {
                      --tw-ring-color: var(--color-blue-500);
                    }
                  }
                  .jtwf .hover\:bg-gray-50 {
                    &:hover {
                      @media (hover: hover) {
                        background-color: var(--color-gray-50);
                      }
                    }
                  }
                  .jtwf .hover\:brightness-95 {
                    &:hover {
                      @media (hover: hover) {
                        --tw-brightness: brightness(95%);
                        filter: var(--tw-blur) var(--tw-brightness)
                          var(--tw-contrast) var(--tw-grayscale)
                          var(--tw-hue-rotate) var(--tw-invert)
                          var(--tw-saturate) var(--tw-sepia)
                          var(--tw-drop-shadow);
                      }
                    }
                  }
                  .jtwf .\[\&\:\:-webkit-date-and-time-value\]\:h-5 {
                    &::-webkit-date-and-time-value {
                      height: calc(var(--spacing) * 5);
                    }
                  }
                  .jtwf .hover\:\[\&\:not\(\:focus-within\)\]\:border-gray-300 {
                    &:hover {
                      @media (hover: hover) {
                        .jtwf &:not(:focus-within) {
                          border-color: var(--color-gray-300);
                        }
                      }
                    }
                  }
                  .jtwf .hover\:\[\&\:not\(\:focus-within\)\]\:bg-gray-50 {
                    &:hover {
                      @media (hover: hover) {
                        .jtwf &:not(:focus-within) {
                          background-color: var(--color-gray-50);
                        }
                      }
                    }
                  }
                }
                @layer base {
                  .jtwf *,
                  .jtwf ::backdrop,
                  .jtwf ::file-selector-button,
                  .jtwf :after,
                  .jtwf :before {
                    border-color: var(--color-gray-200, currentColor);
                  }
                  .jtwf * {
                    border: 0 solid #e5e7eb;
                    box-sizing: border-box;
                  }
                  .jtwf button,
                  .jtwf input,
                  .jtwf optgroup,
                  .jtwf select,
                  .jtwf textarea {
                    color: inherit;
                    font-family: inherit;
                    font-size: 100%;
                    font-weight: inherit;
                    line-height: inherit;
                    margin: 0;
                    padding: 0;
                  }
                }
                @property --tw-space-y-reverse {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0;
                }
                @property --tw-divide-y-reverse {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0;
                }
                @property --tw-border-style {
                  syntax: "*";
                  inherits: false;
                  initial-value: solid;
                }
                @property --tw-font-weight {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-shadow-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-shadow-alpha {
                  syntax: "<percentage>";
                  inherits: false;
                  initial-value: 100%;
                }
                @property --tw-inset-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-inset-shadow-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-inset-shadow-alpha {
                  syntax: "<percentage>";
                  inherits: false;
                  initial-value: 100%;
                }
                @property --tw-ring-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-ring-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-inset-ring-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-inset-ring-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-ring-inset {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-ring-offset-width {
                  syntax: "<length>";
                  inherits: false;
                  initial-value: 0;
                }
                @property --tw-ring-offset-color {
                  syntax: "*";
                  inherits: false;
                  initial-value: #fff;
                }
                @property --tw-ring-offset-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-blur {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-brightness {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-contrast {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-grayscale {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-hue-rotate {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-invert {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-opacity {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-saturate {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-sepia {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-drop-shadow {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-drop-shadow-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-drop-shadow-alpha {
                  syntax: "<percentage>";
                  inherits: false;
                  initial-value: 100%;
                }
                @property --tw-drop-shadow-size {
                  syntax: "*";
                  inherits: false;
                }
                @layer properties {
                  @supports (
                      (-webkit-hyphens: none) and (not (margin-trim: inline))
                    )
                    or
                    (
                      (-moz-orient: inline) and
                        (not (color: rgb(from red r g b)))
                    ) {
                    .jtwf *,
                    .jtwf ::backdrop,
                    .jtwf :after,
                    .jtwf :before {
                      --tw-space-y-reverse: 0;
                      --tw-divide-y-reverse: 0;
                      --tw-border-style: solid;
                      --tw-font-weight: initial;
                      --tw-shadow: 0 0 #0000;
                      --tw-shadow-color: initial;
                      --tw-shadow-alpha: 100%;
                      --tw-inset-shadow: 0 0 #0000;
                      --tw-inset-shadow-color: initial;
                      --tw-inset-shadow-alpha: 100%;
                      --tw-ring-color: initial;
                      --tw-ring-shadow: 0 0 #0000;
                      --tw-inset-ring-color: initial;
                      --tw-inset-ring-shadow: 0 0 #0000;
                      --tw-ring-inset: initial;
                      --tw-ring-offset-width: 0px;
                      --tw-ring-offset-color: #fff;
                      --tw-ring-offset-shadow: 0 0 #0000;
                      --tw-blur: initial;
                      --tw-brightness: initial;
                      --tw-contrast: initial;
                      --tw-grayscale: initial;
                      --tw-hue-rotate: initial;
                      --tw-invert: initial;
                      --tw-opacity: initial;
                      --tw-saturate: initial;
                      --tw-sepia: initial;
                      --tw-drop-shadow: initial;
                      --tw-drop-shadow-color: initial;
                      --tw-drop-shadow-alpha: 100%;
                      --tw-drop-shadow-size: initial;
                    }
                  }
                }
                .jtwf input,
                .jtwf textarea,
                .jtwf select {
                  border-radius: 0.5rem !important; /* Increase border radius (8px) */
                  border: 1px solid #e5e7eb !important; /* Thinner border */
                  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
                  --tw-ring-width: 0 !important; /* Remove the thick ring */
                }

                .jtwf input:focus,
                .jtwf textarea:focus,
                .jtwf select:focus {
                  border-color: #d4a59a !important; /* Primary color border on focus */
                  box-shadow: 0 0 0 2px rgba(212, 165, 154, 0.2) !important; /* Subtle focus shadow */
                  --tw-ring-width: 0 !important;
                }

                /* Style the file input to match other inputs */
                .jtwf input[type="file"] {
                  padding: 0.5rem !important;
                }

                /* Style the submit button to match site theme */
                .jtwf button[data-submit-button="true"] {
                  border-radius: 0.5rem !important;
                  font-weight: 500 !important;
                  transition: all 0.2s ease !important;
                  background-color: #d4a59a !important;
                  color: white !important;
                  padding: 0.75rem 1.5rem !important;
                  width: 100% !important;
                }
              </style>
              <link
                rel="preload"
                as="image"
                href="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
              />
              <form
                class="jtwf w-full"
                data-jobtread-web-form="true"
                data-key="22SP2R8wiQtQ349YSmzU79SwpZgPyAXDuq"
                data-success-url="https://trulypaintingllc.com/thank-you"
              >
                <div
                  class="mx-auto min-w-0 w-full rounded-lg shadow-sm bg-white"
                >
                  <div class="p-4 shadow-line-bottom rounded-t-lg">
                    <img
                      class="block max-w-full max-h-24 mx-auto"
                      src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
                    />
                  </div>
                  <div class="p-4 space-y-2">
                    <label class="cursor-pointer block"
                      ><div class="font-bold">
                        Name<span class="font-normal text-red-500">*</span>
                      </div>
                      <input
                        class="rounded-lg ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        type="text"
                        required=""
                        name="contact.name" /></label
                    ><label class="cursor-pointer block"
                      ><div class="font-bold">
                        Phone<span class="font-normal text-red-500">*</span>
                      </div>
                      <input
                        class="rounded-lg ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        type="tel"
                        required=""
                        name="contact.custom.22NfGfMWhVMG" /></label
                    ><label class="cursor-pointer block"
                      ><div class="font-bold">
                        Email<span class="font-normal text-red-500">*</span>
                      </div>
                      <input
                        class="rounded-lg ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        type="email"
                        required=""
                        name="contact.custom.22NfGfMNnXU5" /></label
                    ><label class="cursor-pointer block"
                      ><div class="font-bold">Address</div>
                      <textarea
                        class="rounded-lg ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        name="location.address"
                      ></textarea></label
                    ><label class="cursor-pointer block"
                      ><div class="font-bold">Files &amp; Photos</div>
                      <input
                        class="rounded-lg ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        type="file"
                        multiple=""
                        name="account.files"
                    /></label>
                    <div class="text-center">
                      <button
                        class="w-full px-4 py-3 rounded-lg shadow-xs cursor-pointer hover:brightness-95 font-semibold"
                        data-submit-button="true"
                        style="background-color: #d4a59a; color: white"
                      >
                        Submit
                      </button>
                    </div>
                  </div>
                </div>
              </form>
              <script async src="https://app.jobtread.com/web-form.js"></script>

              <div class="p-4 text-center">
                <p class="text-sm text-gray-600 italic">
                  "I've heard a lot about your company and you're the only guy I
                  want painting for me" -General Contractor
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Problem/Solution Section -->
    <section class="py-20 bg-gray-50">
      <div class="container mx-auto px-4 md:px-6">
        <div class="max-w-4xl mx-auto">
          <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">
            <span class="rose-gold-gradient"
              >The Truly Painting Difference</span
            >
          </h2>

          <div class="space-y-6">
            <div class="grid md:grid-cols-2 gap-4">
              <div class="bg-primary/10 rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-red-500 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Other Painting Companies
                  </h3>
                </div>
                <p class="text-gray-700">
                  Color uncertainty, difficult to visualize final results
                </p>
              </div>

              <div class="bg-primary rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-white mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <h3 class="text-lg font-semibold text-white">Our Approach</h3>
                </div>
                <p class="text-white">
                  Free AI preview app lets you see your colors before we start
                </p>
              </div>
            </div>

            <div class="grid md:grid-cols-2 gap-4">
              <div class="bg-primary/10 rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-red-500 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Other Painting Companies
                  </h3>
                </div>
                <p class="text-gray-700">
                  Messy work sites, paint splatters everywhere
                </p>
              </div>

              <div class="bg-primary rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-white mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <h3 class="text-lg font-semibold text-white">Our Approach</h3>
                </div>
                <p class="text-white">
                  Meticulous preparation and thorough clean-up after every
                  project
                </p>
              </div>
            </div>

            <div class="grid md:grid-cols-2 gap-4">
              <div class="bg-primary/10 rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-red-500 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Other Painting Companies
                  </h3>
                </div>
                <p class="text-gray-700">Low-quality paints that don't last</p>
              </div>

              <div class="bg-primary rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-white mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <h3 class="text-lg font-semibold text-white">Our Approach</h3>
                </div>
                <p class="text-white">
                  Premium quality paints and materials for lasting beauty
                </p>
              </div>
            </div>

            <div class="grid md:grid-cols-2 gap-4">
              <div class="bg-primary/10 rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-red-500 mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="15" y1="9" x2="9" y2="15"></line>
                    <line x1="9" y1="9" x2="15" y2="15"></line>
                  </svg>
                  <h3 class="text-lg font-semibold text-primary">
                    Other Painting Companies
                  </h3>
                </div>
                <p class="text-gray-700">
                  Hidden fees, unexpected costs, vague quotes
                </p>
              </div>

              <div class="bg-primary rounded-lg p-6">
                <div class="flex items-center mb-3">
                  <svg
                    class="w-6 h-6 text-white mr-2"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                  <h3 class="text-lg font-semibold text-white">Our Approach</h3>
                </div>
                <p class="text-white">
                  Clear, detailed quotes with no surprise charges
                </p>
              </div>
            </div>
          </div>

          <div class="mt-12 text-center">
            <p class="text-xl text-primary font-semibold">
              "The AI preview was amazing - I was able to see exactly how my
              home would look before they even started!"
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="py-20 bg-secondary">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold mb-4">
            <span class="text-white relative">
              Professional Painting Services in Newport
              <span
                class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-primary rounded-full"
              ></span>
            </span>
          </h2>
          <p class="text-xl text-gray-200 max-w-3xl mx-auto">
            We provide exceptional interior and exterior painting services with
            meticulous attention to detail and premium materials for lasting
            beauty.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Service 1 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url(https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e447581dc600e9478fe95.webp);
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Interior Painting</h3>
                <p class="text-sm text-white">Transform Your Living Spaces</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Expert interior painting for walls, ceilings, trim, doors, and
              more with flawless finishes and attention to detail.
            </p>
          </div>

          <!-- Service 2 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f6c40216f46e42ba9.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Exterior Painting</h3>
                <p class="text-sm text-white">Boost Your Curb Appeal</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Comprehensive exterior painting services for siding, trim, doors,
              and more with weather-resistant finishes.
            </p>
          </div>

          <!-- Service 3 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url(https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e447579f5947398ed6e6f.webp);
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Cabinet Refinishing</h3>
                <p class="text-sm text-white">Kitchen & Bath Transformations</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Give your kitchen or bathroom a fresh new look with our expert
              cabinet painting and refinishing services.
            </p>
          </div>

          <!-- Service 4 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f096f56e0e2651ec6.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Color Consultation</h3>
                <p class="text-sm text-white">Expert Color Selection</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Professional color consultation to help you choose the perfect
              colors for your home, enhanced by our AI preview app.
            </p>
          </div>

          <!-- Service 5 -->
          <div class="group">
            <div
              class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
            >
              <div
                class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                style="
                  background-image: url('https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457fb4903b5d4acf3d78.webp');
                "
              ></div>
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
              ></div>
              <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 class="text-xl font-bold">Deck & Fence Staining</h3>
                <p class="text-sm text-white">Outdoor Surface Protection</p>
              </div>
            </div>
            <p class="mt-2 text-gray-200 text-sm">
              Protect and beautify your outdoor wooden surfaces with our
              professional staining and sealing services.
            </p>
          </div>

          <!-- Service 6 -->
          <div class="group">
            <a href="visualizer.html" class="block">
              <div
                class="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]"
              >
                <div
                  class="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                  style="
                    background-image: url('https://images.leadconnectorhq.com/image/f_webp/q_80/r_800/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f096f560eec651ec7.webp');
                  "
                ></div>
                <div
                  class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
                ></div>
                <div class="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 class="text-xl font-bold">AI Color Preview</h3>
                  <p class="text-sm text-white">See Before We Paint</p>
                </div>
                <div
                  class="absolute top-3 right-3 bg-primary text-white text-xs font-bold py-1 px-2 rounded"
                >
                  Try Now!
                </div>
              </div>
              <p class="mt-2 text-gray-200 text-sm">
                Our exclusive AI app lets you visualize any color on your home
                before we start painting - eliminating color uncertainty.
              </p>
            </a>
          </div>
        </div>

        <div class="mt-12 text-center">
          <a
            href="#contact"
            class="inline-flex items-center px-8 py-4 bg-white hover:bg-gray-100 text-primary font-semibold rounded-md transition-colors"
          >
            Get Your Free Painting Estimate Today
          </a>
        </div>
      </div>
    </section>

    <!-- AI Preview App Section -->
    <section class="py-20 bg-white">
      <div class="container mx-auto px-4 md:px-6">
        <div class="max-w-6xl mx-auto">
          <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">
              <span class="rose-gold-gradient"
                >See Your Home's Future: Try Our AI Paint Preview App!</span
              >
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              Eliminate color doubt with our innovative AI tool that lets you
              visualize any paint color on your home before we start painting.
            </p>
          </div>

          <div class="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <img
                src="https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f9a0ad36873275d5b.webp"
                alt="AI Paint Preview App Demo"
                class="rounded-lg shadow-xl w-full"
              />
            </div>

            <div>
              <h3 class="text-2xl font-bold mb-6">How It Works:</h3>
              <div class="space-y-6">
                <div class="flex items-start">
                  <div
                    class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0"
                  >
                    1
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold mb-2">Take a Photo</h4>
                    <p class="text-gray-600">
                      Snap a photo of your home's interior or exterior that you
                      want to paint.
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div
                    class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0"
                  >
                    2
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold mb-2">Choose Colors</h4>
                    <p class="text-gray-600">
                      Select from thousands of paint colors or upload your own
                      custom shade.
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div
                    class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0"
                  >
                    3
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold mb-2">See the Results</h4>
                    <p class="text-gray-600">
                      Our AI instantly generates a realistic preview of how your
                      space will look with the new color.
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <div
                    class="bg-primary text-white rounded-full w-8 h-8 flex items-center justify-center font-bold mr-4 flex-shrink-0"
                  >
                    4
                  </div>
                  <div>
                    <h4 class="text-lg font-semibold mb-2">Share & Compare</h4>
                    <p class="text-gray-600">
                      Compare different colors side by side and share with
                      family and friends to get their opinion.
                    </p>
                  </div>
                </div>
              </div>

              <div class="mt-8 flex flex-col sm:flex-row gap-4">
                <a
                  href="visualizer.html"
                  class="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-md transition"
                >
                  Try Our AI Preview App Now!
                </a>
                <a
                  href="#contact"
                  class="inline-flex items-center px-6 py-3 border border-primary text-primary hover:bg-primary/10 font-semibold rounded-md transition"
                >
                  Get a Free Quote
                </a>
              </div>
            </div>
          </div>

          <div class="mt-16 bg-gray-50 rounded-xl p-8 shadow-md">
            <div class="text-center mb-8">
              <h3 class="text-2xl font-bold mb-2">Why Our AI Preview App?</h3>
              <p class="text-gray-600">
                Take the guesswork out of choosing paint colors and make
                confident decisions.
              </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="text-primary mb-4">
                  <svg
                    class="w-10 h-10"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
                    ></path>
                  </svg>
                </div>
                <h4 class="text-lg font-semibold mb-2">
                  Eliminate Uncertainty
                </h4>
                <p class="text-gray-600">
                  No more guessing how a color will look in your space - see it
                  before you commit.
                </p>
              </div>

              <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="text-primary mb-4">
                  <svg
                    class="w-10 h-10"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                    <line x1="9" y1="9" x2="9.01" y2="9"></line>
                    <line x1="15" y1="9" x2="15.01" y2="9"></line>
                  </svg>
                </div>
                <h4 class="text-lg font-semibold mb-2">
                  Customer Satisfaction
                </h4>
                <p class="text-gray-600">
                  Our clients love knowing exactly what to expect before we
                  start painting.
                </p>
              </div>

              <div class="bg-white p-6 rounded-lg shadow-sm">
                <div class="text-primary mb-4">
                  <svg
                    class="w-10 h-10"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"
                    ></path>
                  </svg>
                </div>
                <h4 class="text-lg font-semibold mb-2">Completely Free</h4>
                <p class="text-gray-600">
                  Our AI preview app is free to use - just another way we
                  provide exceptional service.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Trust Section -->
    <section id="trust" class="py-20 bg-gray-50">
      <div class="container mx-auto px-4 md:px-6">
        <div class="text-center max-w-3xl mx-auto mb-16">
          <h2 class="text-3xl md:text-4xl font-bold mb-6">
            The Painting Company Newport Homeowners Trust
          </h2>
          <p class="text-gray-600 text-lg">
            Our reputation across hundreds of projects speaks for itself. We
            pride ourselves on reliability, quality, and doing the job right the
            first time.
          </p>
        </div>

        <!-- Testimonials Section (Archived) -->
        <div class="bg-white rounded-lg shadow-md p-8 text-center mb-12">
          <div class="flex justify-center mb-6">
            <svg
              class="w-12 h-12 text-primary"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-4">Testimonials Coming Soon</h3>
          <p class="text-gray-600 text-lg max-w-2xl mx-auto">
            We're in the process of collecting real testimonials from our
            satisfied customers. Check back soon to read about the experiences
            of homeowners who have trusted us with their painting projects.
          </p>
        </div>

        <div class="bg-secondary rounded-xl shadow-xl overflow-hidden">
          <div class="p-8 md:p-12">
            <div class="max-w-4xl mx-auto text-center text-white">
              <h3 class="text-2xl md:text-3xl font-bold mb-6">
                What Sets Us Apart
              </h3>

              <div
                class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mt-8"
              >
                <div class="flex flex-col items-center">
                  <div
                    class="bg-white rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4"
                  >
                    <svg
                      class="w-8 h-8"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"
                      ></path>
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold mb-2">Premium Materials</h4>
                  <p class="text-gray-200">
                    We use only high-quality paints and materials for lasting
                    results
                  </p>
                </div>

                <div class="flex flex-col items-center">
                  <div
                    class="bg-white rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4"
                  >
                    <svg
                      class="w-8 h-8"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"
                      ></path>
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold mb-2">AI Color Preview</h4>
                  <p class="text-gray-200">
                    See your colors before we paint with our innovative app
                  </p>
                </div>

                <div class="flex flex-col items-center">
                  <div
                    class="bg-white rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4"
                  >
                    <svg
                      class="w-8 h-8"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path
                        d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
                      ></path>
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold mb-2">
                    Expert Consultation
                  </h4>
                  <p class="text-gray-200">
                    Professional color and design advice for your project
                  </p>
                </div>

                <div class="flex flex-col items-center">
                  <div
                    class="bg-white rounded-full w-16 h-16 flex items-center justify-center text-primary mb-4"
                  >
                    <svg
                      class="w-8 h-8"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <polygon
                        points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"
                      ></polygon>
                    </svg>
                  </div>
                  <h4 class="text-lg font-semibold mb-2">Meticulous Prep</h4>
                  <p class="text-gray-200">
                    Thorough preparation and clean-up for flawless results
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Quick Contact Section -->
    <section class="py-16 bg-secondary text-white">
      <div class="container mx-auto px-4 md:px-6">
        <div
          class="max-w-4xl mx-auto flex flex-col md:flex-row items-center justify-between"
        >
          <div class="mb-8 md:mb-0 text-center md:text-left">
            <div class="flex items-center justify-center md:justify-start mb-4">
              <svg
                class="w-8 h-8 mr-3 text-primary"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12 6 12 12 16 14"></polyline>
              </svg>
              <h2 class="text-2xl md:text-3xl font-bold">
                Ready to Transform Your Home?
              </h2>
            </div>
            <p class="text-xl mb-2">
              Professional painting services in Newport
            </p>
            <p class="text-lg opacity-90">
              Get expert advice and a free color consultation for your painting
              project
            </p>
          </div>

          <div class="flex-shrink-0">
            <a
              href="tel:5094617090"
              class="flex items-center bg-primary hover:bg-primary/90 text-white px-6 py-4 rounded-lg text-xl font-semibold transition transform hover:scale-105"
            >
              <svg
                class="w-6 h-6 mr-3"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
              Call: (*************
            </a>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
      <div class="container mx-auto px-4 md:px-6">
        <div class="max-w-5xl mx-auto">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-6">
              <span class="relative inline-block">
                <span class="rose-gold-gradient"
                  >Ready to Transform Your Home with Fresh Paint?</span
                >
                <span
                  class="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-[#D4A59A] to-[#C69489] rounded-full transform scale-x-0 transition-transform duration-500 group-hover:scale-x-100"
                ></span>
              </span>
            </h2>
            <p class="text-gray-600 text-lg max-w-3xl mx-auto">
              Get a free consultation and estimate for your painting project
              today. Try our AI preview app to see your new colors before we
              start!
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div class="bg-gray-50 rounded-lg shadow-md p-8">
              <h3 class="text-2xl font-bold mb-6 text-primary">Contact Us</h3>

              <div class="space-y-6">
                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                    ></path>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Call Us Directly</h4>
                    <p class="text-gray-700 mb-1">(*************</p>
                    <p class="text-sm text-gray-500">
                      For consultations and estimates
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Email Us</h4>
                    <p class="text-gray-700 mb-1"><EMAIL></p>
                    <p class="text-sm text-gray-500">
                      For detailed inquiries and quotes
                    </p>
                  </div>
                </div>

                <div class="flex items-start">
                  <svg
                    class="w-[22px] h-[22px] text-primary mr-4 mt-1 flex-shrink-0"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                  <div>
                    <h4 class="font-semibold text-lg mb-1">Hours</h4>
                    <p class="text-gray-700 mb-1">Monday-Friday: 8am-6pm</p>
                    <p class="text-sm text-gray-500">
                      Serving Newport and surrounding areas
                    </p>
                  </div>
                </div>
              </div>

              <div class="mt-8 pt-6 border-t border-gray-200">
                <h4 class="font-semibold text-lg mb-3">Visit Our Office</h4>
                <div class="flex items-start mb-4">
                  <svg
                    class="w-5 h-5 text-primary mr-2 mt-1"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path
                      d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                    ></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                  <div>
                    <p class="text-gray-700">141 Elu Beach Rd</p>
                    <p class="text-gray-700">Newport, WA 99156</p>
                  </div>
                </div>

                <h4 class="font-semibold text-lg mb-3 mt-6">
                  Professional Credentials
                </h4>
                <ul class="space-y-2 text-gray-700">
                  <li class="flex items-center">
                    <svg
                      class="w-4 h-4 text-primary mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    Licensed Professional Painters
                  </li>
                  <li class="flex items-center">
                    <svg
                      class="w-4 h-4 text-primary mr-2"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    Fully Insured and Bonded
                  </li>
                </ul>
              </div>
            </div>

            <div
              class="bg-white rounded-lg shadow-md p-6 border border-gray-200"
            >
              <h3 class="text-2xl font-bold mb-6 text-primary">
                Request Your Free Estimate
              </h3>
              <!-- JobTread Form -->
              <style>
                @layer properties;
                @layer theme, base, components, utilities;
                @layer theme {
                  .jtwf {
                    --color-red-500: oklch(63.7% 0.237 25.331);
                    --color-blue-500: oklch(62.3% 0.214 259.815);
                    --color-gray-50: oklch(98.5% 0.002 247.839);
                    --color-gray-200: oklch(92.8% 0.006 264.531);
                    --color-gray-300: oklch(87.2% 0.01 258.338);
                    --color-gray-500: oklch(55.1% 0.027 264.364);
                    --color-white: #fff;
                    --spacing: 0.25rem;
                    --container-sm: 24rem;
                    --text-xl: 1.25rem;
                    --text-xl--line-height: 1.4;
                    --font-weight-normal: 400;
                    --font-weight-bold: 700;
                    --radius-sm: 0.25rem;
                    --default-transition-duration: 250ms;
                    --default-transition-timing-function: cubic-bezier(
                      0.4,
                      0,
                      0.2,
                      1
                    );
                  }
                }
                @layer utilities {
                  .jtwf .mx-auto {
                    margin-inline: auto;
                  }
                  .jtwf .block {
                    display: block;
                  }
                  .jtwf .flex {
                    display: flex;
                  }
                  .jtwf .max-h-24 {
                    max-height: calc(var(--spacing) * 24);
                  }
                  .jtwf .w-full {
                    width: 100%;
                  }
                  .jtwf .max-w-full {
                    max-width: 100%;
                  }
                  .jtwf .max-w-sm {
                    max-width: var(--container-sm);
                  }
                  .jtwf .min-w-0 {
                    min-width: calc(var(--spacing) * 0);
                  }
                  .jtwf .shrink-0 {
                    flex-shrink: 0;
                  }
                  .jtwf .grow {
                    flex-grow: 1;
                  }
                  .jtwf .cursor-pointer {
                    cursor: pointer;
                  }
                  .jtwf .appearance-none {
                    appearance: none;
                  }
                  .jtwf .items-center {
                    align-items: center;
                  }
                  .jtwf .gap-2 {
                    gap: calc(var(--spacing) * 2);
                  }
                  .jtwf .space-y-2 {
                    :where(& > :not(:last-child)) {
                      --tw-space-y-reverse: 0;
                      margin-block-end: calc(
                        var(--spacing) * 2 * (1 - var(--tw-space-y-reverse))
                      );
                      margin-block-start: calc(
                        var(--spacing) * 2 * var(--tw-space-y-reverse)
                      );
                    }
                  }
                  .jtwf .divide-y {
                    :where(& > :not(:last-child)) {
                      --tw-divide-y-reverse: 0;
                      border-bottom-style: var(--tw-border-style);
                      border-bottom-width: calc(
                        1px * (1 - var(--tw-divide-y-reverse))
                      );
                      border-top-style: var(--tw-border-style);
                      border-top-width: calc(1px * var(--tw-divide-y-reverse));
                    }
                  }
                  .jtwf .overflow-hidden {
                    overflow: hidden;
                  }
                  .jtwf .rounded-sm {
                    border-radius: var(--radius-sm);
                  }
                  .jtwf .border {
                    border-style: var(--tw-border-style);
                    border-width: 1px;
                  }
                  .jtwf .bg-white {
                    background-color: var(--color-white);
                  }
                  .jtwf .p-2 {
                    padding: calc(var(--spacing) * 2);
                  }
                  .jtwf .p-4 {
                    padding: calc(var(--spacing) * 4);
                  }
                  .jtwf .px-4 {
                    padding-inline: calc(var(--spacing) * 4);
                  }
                  .jtwf .py-2 {
                    padding-block: calc(var(--spacing) * 2);
                  }
                  .jtwf .text-center {
                    text-align: center;
                  }
                  .jtwf .text-right {
                    text-align: right;
                  }
                  .jtwf .text-xl {
                    font-size: var(--text-xl);
                    line-height: var(--tw-leading, var(--text-xl--line-height));
                  }
                  .jtwf .font-bold {
                    --tw-font-weight: var(--font-weight-bold);
                    font-weight: var(--font-weight-bold);
                  }
                  .jtwf .font-normal {
                    --tw-font-weight: var(--font-weight-normal);
                    font-weight: var(--font-weight-normal);
                  }
                  .jtwf .text-gray-500 {
                    color: var(--color-gray-500);
                  }
                  .jtwf .text-red-500 {
                    color: var(--color-red-500);
                  }
                  .jtwf .shadow-sm {
                    --tw-shadow: 0 1px 3px 0
                        var(--tw-shadow-color, rgba(0, 0, 0, 0.1)),
                      0 1px 2px -1px var(--tw-shadow-color, rgba(0, 0, 0, 0.1));
                  }
                  .jtwf .shadow-sm,
                  .jtwf .shadow-xs {
                    box-shadow: var(--tw-inset-shadow),
                      var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow),
                      var(--tw-ring-shadow), var(--tw-shadow);
                  }
                  .jtwf .shadow-xs {
                    --tw-shadow: 0 1px 2px 0
                      var(--tw-shadow-color, rgba(0, 0, 0, 0.05));
                  }
                  .jtwf .ring {
                    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
                      calc(1px + var(--tw-ring-offset-width))
                      var(--tw-ring-color, currentcolor);
                    box-shadow: var(--tw-inset-shadow),
                      var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow),
                      var(--tw-ring-shadow), var(--tw-shadow);
                  }
                  .jtwf .ring-gray-200 {
                    --tw-ring-color: var(--color-gray-200);
                  }
                  .jtwf .transition {
                    transition-duration: var(
                      --tw-duration,
                      var(--default-transition-duration)
                    );
                    transition-property: color, background-color, border-color,
                      outline-color, text-decoration-color, fill, stroke,
                      --tw-gradient-from, --tw-gradient-via, --tw-gradient-to,
                      opacity, box-shadow, transform, translate, scale, rotate,
                      filter, -webkit-backdrop-filter, backdrop-filter, display,
                      visibility, content-visibility, overlay, pointer-events;
                    transition-timing-function: var(
                      --tw-ease,
                      var(--default-transition-timing-function)
                    );
                  }
                  .jtwf .focus-within\:bg-white {
                    &:focus-within {
                      background-color: var(--color-white);
                    }
                  }
                  .jtwf .focus-within\:ring-3 {
                    &:focus-within {
                      --tw-ring-shadow: var(--tw-ring-inset) 0 0 0
                        calc(3px + var(--tw-ring-offset-width))
                        var(--tw-ring-color, currentcolor);
                      box-shadow: var(--tw-inset-shadow),
                        var(--tw-inset-ring-shadow),
                        var(--tw-ring-offset-shadow), var(--tw-ring-shadow),
                        var(--tw-shadow);
                    }
                  }
                  .jtwf .focus-within\:ring-blue-500 {
                    &:focus-within {
                      --tw-ring-color: var(--color-blue-500);
                    }
                  }
                  .jtwf .hover\:bg-gray-50 {
                    &:hover {
                      @media (hover: hover) {
                        background-color: var(--color-gray-50);
                      }
                    }
                  }
                  .jtwf .hover\:brightness-95 {
                    &:hover {
                      @media (hover: hover) {
                        --tw-brightness: brightness(95%);
                        filter: var(--tw-blur) var(--tw-brightness)
                          var(--tw-contrast) var(--tw-grayscale)
                          var(--tw-hue-rotate) var(--tw-invert)
                          var(--tw-saturate) var(--tw-sepia)
                          var(--tw-drop-shadow);
                      }
                    }
                  }
                  .jtwf .\[\&\:\:-webkit-date-and-time-value\]\:h-5 {
                    &::-webkit-date-and-time-value {
                      height: calc(var(--spacing) * 5);
                    }
                  }
                  .jtwf .hover\:\[\&\:not\(\:focus-within\)\]\:border-gray-300 {
                    &:hover {
                      @media (hover: hover) {
                        .jtwf &:not(:focus-within) {
                          border-color: var(--color-gray-300);
                        }
                      }
                    }
                  }
                  .jtwf .hover\:\[\&\:not\(\:focus-within\)\]\:bg-gray-50 {
                    &:hover {
                      @media (hover: hover) {
                        .jtwf &:not(:focus-within) {
                          background-color: var(--color-gray-50);
                        }
                      }
                    }
                  }
                }
                @layer base {
                  .jtwf *,
                  .jtwf ::backdrop,
                  .jtwf ::file-selector-button,
                  .jtwf :after,
                  .jtwf :before {
                    border-color: var(--color-gray-200, currentColor);
                  }
                  .jtwf * {
                    border: 0 solid #e5e7eb;
                    box-sizing: border-box;
                  }
                  .jtwf button,
                  .jtwf input,
                  .jtwf optgroup,
                  .jtwf select,
                  .jtwf textarea {
                    color: inherit;
                    font-family: inherit;
                    font-size: 100%;
                    font-weight: inherit;
                    line-height: inherit;
                    margin: 0;
                    padding: 0;
                  }
                }
                @property --tw-space-y-reverse {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0;
                }
                @property --tw-divide-y-reverse {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0;
                }
                @property --tw-border-style {
                  syntax: "*";
                  inherits: false;
                  initial-value: solid;
                }
                @property --tw-font-weight {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-shadow-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-shadow-alpha {
                  syntax: "<percentage>";
                  inherits: false;
                  initial-value: 100%;
                }
                @property --tw-inset-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-inset-shadow-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-inset-shadow-alpha {
                  syntax: "<percentage>";
                  inherits: false;
                  initial-value: 100%;
                }
                @property --tw-ring-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-ring-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-inset-ring-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-inset-ring-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-ring-inset {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-ring-offset-width {
                  syntax: "<length>";
                  inherits: false;
                  initial-value: 0;
                }
                @property --tw-ring-offset-color {
                  syntax: "*";
                  inherits: false;
                  initial-value: #fff;
                }
                @property --tw-ring-offset-shadow {
                  syntax: "*";
                  inherits: false;
                  initial-value: 0 0 #0000;
                }
                @property --tw-blur {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-brightness {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-contrast {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-grayscale {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-hue-rotate {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-invert {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-opacity {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-saturate {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-sepia {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-drop-shadow {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-drop-shadow-color {
                  syntax: "*";
                  inherits: false;
                }
                @property --tw-drop-shadow-alpha {
                  syntax: "<percentage>";
                  inherits: false;
                  initial-value: 100%;
                }
                @property --tw-drop-shadow-size {
                  syntax: "*";
                  inherits: false;
                }
                @layer properties {
                  @supports (
                      (-webkit-hyphens: none) and (not (margin-trim: inline))
                    )
                    or
                    (
                      (-moz-orient: inline) and
                        (not (color: rgb(from red r g b)))
                    ) {
                    .jtwf *,
                    .jtwf ::backdrop,
                    .jtwf :after,
                    .jtwf :before {
                      --tw-space-y-reverse: 0;
                      --tw-divide-y-reverse: 0;
                      --tw-border-style: solid;
                      --tw-font-weight: initial;
                      --tw-shadow: 0 0 #0000;
                      --tw-shadow-color: initial;
                      --tw-shadow-alpha: 100%;
                      --tw-inset-shadow: 0 0 #0000;
                      --tw-inset-shadow-color: initial;
                      --tw-inset-shadow-alpha: 100%;
                      --tw-ring-color: initial;
                      --tw-ring-shadow: 0 0 #0000;
                      --tw-inset-ring-color: initial;
                      --tw-inset-ring-shadow: 0 0 #0000;
                      --tw-ring-inset: initial;
                      --tw-ring-offset-width: 0px;
                      --tw-ring-offset-color: #fff;
                      --tw-ring-offset-shadow: 0 0 #0000;
                      --tw-blur: initial;
                      --tw-brightness: initial;
                      --tw-contrast: initial;
                      --tw-grayscale: initial;
                      --tw-hue-rotate: initial;
                      --tw-invert: initial;
                      --tw-opacity: initial;
                      --tw-saturate: initial;
                      --tw-sepia: initial;
                      --tw-drop-shadow: initial;
                      --tw-drop-shadow-color: initial;
                      --tw-drop-shadow-alpha: 100%;
                      --tw-drop-shadow-size: initial;
                    }
                  }
                }
                .jtwf input,
                .jtwf textarea,
                .jtwf select {
                  border-radius: 0.5rem !important; /* Increase border radius (8px) */
                  border: 1px solid #e5e7eb !important; /* Thinner border */
                  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
                  --tw-ring-width: 0 !important; /* Remove the thick ring */
                }

                .jtwf input:focus,
                .jtwf textarea:focus,
                .jtwf select:focus {
                  border-color: #d4a59a !important; /* Primary color border on focus */
                  box-shadow: 0 0 0 2px rgba(212, 165, 154, 0.2) !important; /* Subtle focus shadow */
                  --tw-ring-width: 0 !important;
                }

                /* Style the file input to match other inputs */
                .jtwf input[type="file"] {
                  padding: 0.5rem !important;
                }

                /* Style the submit button to match site theme */
                .jtwf button[data-submit-button="true"] {
                  border-radius: 0.5rem !important;
                  font-weight: 500 !important;
                  transition: all 0.2s ease !important;
                  background-color: #d4a59a !important;
                  color: white !important;
                  padding: 0.75rem 1.5rem !important;
                  width: 100% !important;
                }
              </style>
              <link
                rel="preload"
                as="image"
                href="https://cdn.jobtread.com/G1IASBwHdkw8xHbou6bBc-Tjat4eKdBhTx2WvhSJeWtFBPIoKhUCyq_BcvPhD6QlQ53g3FkquIFWgIC2x_MlnNdDMpSTH2MNciCaMw.Wo-c9cmNlJNvsqOMmDnssEy4ITpvdH84lbjcwQyU6fI?size=1024"
              />
              <form
                class="jtwf"
                data-jobtread-web-form="true"
                data-key="22SP2R8wiQtQ349YSmzU79SwpZgPyAXDuq"
                data-success-url="https://trulypaintingllc.com/thank-you"
              >
                <div
                  class="mx-auto min-w-0 max-w-sm rounded-sm shadow-sm bg-white"
                >
                  <div class="p-4 shadow-line-bottom">
                    <img
                      class="block max-w-full max-h-24 mx-auto"
                      src="https://cdn.jobtread.com/G1IASBwHdkw8xHbou6bBc-Tjat4eKdBhTx2WvhSJeWtFBPIoKhUCyq_BcvPhD6QlQ53g3FkquIFWgIC2x_MlnNdDMpSTH2MNciCaMw.Wo-c9cmNlJNvsqOMmDnssEy4ITpvdH84lbjcwQyU6fI?size=1024"
                    />
                  </div>
                  <div class="p-4 space-y-2">
                    <label class="cursor-pointer block">
                      <div class="font-bold">
                        Name<span class="font-normal text-red-500">*</span>
                      </div>
                      <input
                        class="rounded-sm ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        type="text"
                        required=""
                        name="contact.name"
                      />
                    </label>
                    <label class="cursor-pointer block">
                      <div class="font-bold">
                        Phone<span class="font-normal text-red-500">*</span>
                      </div>
                      <input
                        class="rounded-sm ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        type="tel"
                        required=""
                        name="contact.custom.22NfGfMWhVMG"
                      />
                    </label>
                    <label class="cursor-pointer block">
                      <div class="font-bold">
                        Email<span class="font-normal text-red-500">*</span>
                      </div>
                      <input
                        class="rounded-sm ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        type="email"
                        required=""
                        name="contact.custom.22NfGfMNnXU5"
                      />
                    </label>
                    <label class="cursor-pointer block">
                      <div class="font-bold">Address</div>
                      <textarea
                        class="rounded-sm ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        name="location.address"
                      ></textarea>
                    </label>
                    <label class="cursor-pointer block">
                      <div class="font-bold">Files &amp; Photos</div>
                      <input
                        class="rounded-sm ring ring-gray-200 p-2 w-full bg-white appearance-none transition hover:[&amp;:not(:focus-within)]:bg-gray-50 hover:[&amp;:not(:focus-within)]:border-gray-300 focus-within:bg-white focus-within:ring-3 focus-within:ring-blue-500 [&amp;::-webkit-date-and-time-value]:h-5"
                        type="file"
                        multiple=""
                        name="account.files"
                      />
                    </label>
                    <div class="text-right">
                      <button
                        class="text-right px-4 py-2 rounded-sm shadow-xs cursor-pointer hover:brightness-95"
                        data-submit-button="true"
                        style="background-color: #d39382; color: #fbf4f3"
                      >
                        Submit
                      </button>
                    </div>
                  </div>
                </div>
              </form>
              <script async src="https://app.jobtread.com/web-form.js"></script>

              <div class="p-4 text-center">
                <p class="text-sm text-gray-600 italic">
                  "I've heard a lot about your company and you're the only guy I
                  want painting for me" -General Contractor
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-secondary text-white py-12">
      <div class="container mx-auto px-4 md:px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <img
              src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
              alt="Truly Painting Logo"
              class="h-14 mb-4"
            />
            <p class="text-gray-300 mb-4">
              Newport's professional painting services. Quality craftsmanship,
              innovative solutions, and our exclusive AI preview app.
            </p>
            <div class="flex space-x-4">
              <a
                href="https://www.facebook.com/profile.php?id=61557209638007"
                target="_blank"
                class="text-primary hover:text-primary/80 transition"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"
                  ></path>
                </svg>
              </a>
              <a
                href="https://www.instagram.com/trulypainting/"
                target="_blank"
                class="text-primary hover:text-primary/80 transition"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path
                    d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"
                  ></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
            <ul class="space-y-3">
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <a href="tel:5094617090" class="hover:text-primary transition"
                  >(*************</a
                >
              </li>
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="hover:text-primary transition"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-start">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary mt-1"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                  ></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <span>141 Elu Beach Rd, Newport, WA 99156</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Hours & Information</h3>
            <ul class="space-y-2 text-gray-300">
              <li>Monday-Friday: 8am-6pm</li>
              <li>Serving Newport & Surrounding Areas</li>
              <li>Fully Insured and Bonded</li>
              <li>Professional Painting Services</li>
              <li>Free AI Color Preview App</li>
            </ul>
          </div>
        </div>

        <div
          class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
        >
          <p>
            ©
            <script>
              document.write(new Date().getFullYear());
            </script>
            Truly Painting. All rights reserved.
          </p>
          <p class="mt-2">
            <a href="privacy.html" class="hover:text-primary transition"
              >Privacy Policy</a
            >
            |
            <a href="terms.html" class="hover:text-primary transition"
              >Terms of Service</a
            >
          </p>
        </div>
      </div>
    </footer>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-NBKXB3TF"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- Chat Widget  -->
    <div
      data-chat-widget
      data-widget-id="6802ed1930666bcb1727d04c"
      data-location-id="7mewc5RIQe9kefPF1P31"
    ></div>
    <script
      src="https://widgets.leadconnectorhq.com/loader.js"
      data-resources-url="https://widgets.leadconnectorhq.com/chat-widget/loader.js"
      data-widget-id="6802ed1930666bcb1727d04c"
    ></script>
    <!-- End Chat Widget  -->
  </body>
</html>

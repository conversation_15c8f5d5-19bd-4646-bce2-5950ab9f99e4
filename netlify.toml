[build]
  publish = "dist"
  command = "npm run build"

# Redirect and rewrite rules
# Redirect www to non-www
[[redirects]]
  from = "https://www.trulypaintingllc.com/*"
  to = "https://trulypaintingllc.com/:splat"
  status = 301
  force = true

# Handle thank-you page specifically
[[redirects]]
  from = "/thank-you"
  to = "/thank-you.html"
  status = 200

# SPA fallback
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false
  conditions = {Path = {match = "^(?!.*\\.).*$"}}

# Headers for caching and security
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "no-referrer-when-downgrade"
    Content-Security-Policy = "default-src 'self' https://storage.googleapis.com https://cdn.tailwindcss.com https://fonts.googleapis.com https://fonts.gstatic.com https://images.pexels.com https://sdk.gleap.io https://app.jobtread.com https://cdn.jobtread.com https://link.msgsndr.com https://widgets.leadconnectorhq.com https://stcdn.leadconnectorhq.com https://services.leadconnectorhq.com https://*.doubleclick.net; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://sdk.gleap.io https://app.jobtread.com https://link.msgsndr.com https://widgets.leadconnectorhq.com https://www.googletagmanager.com https://stcdn.leadconnectorhq.com https://services.leadconnectorhq.com https://www.google-analytics.com https://www.googleadservices.com https://*.doubleclick.net https://static.cloudflareinsights.com https://www.google.com https://googleads.g.doubleclick.net https://www.gstatic.com https://app.jobtread.com https://link.msgsndr.com https://widgets.leadconnectorhq.com https://cdn.jobtread.com https://services.msgsndr.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.bunny.net https://stcdn.leadconnectorhq.com; img-src 'self' https://storage.googleapis.com https://images.pexels.com https://images.unsplash.com https://images.leadconnectorhq.com https://cdn.jobtread.com https://www.googleadservices.com https://www.google-analytics.com https://*.doubleclick.net https://www.google.com https://googleads.g.doubleclick.net data: https://*.cdninstagram.com https://*.fbcdn.net; font-src 'self' https://fonts.gstatic.com https://fonts.bunny.net data:; connect-src 'self' https://api.gleap.io wss://ws.gleap.io https://app.jobtread.com https://api.leadconnectorhq.com https://services.leadconnectorhq.com https://api.jobtread.com https://widgets.leadconnectorhq.com https://www.google-analytics.com https://www.googleadservices.com https://www.google.com https://*.jobtread.com https://*.leadconnectorhq.com https://*.doubleclick.net https://static.cloudflareinsights.com https://stats.g.doubleclick.net https://services.msgsndr.com https://storage.googleapis.com; media-src 'self' blob:; object-src 'none'; frame-src https://messenger-app.gleap.io https://www.instagram.com https://instagram.com https://*.instagram.com https://vision.stokeleads.com https://api.leadconnectorhq.com https://api.leadconnectorhq.com https://stcdn.leadconnectorhq.com https://tpc.googlesyndication.com https://*.doubleclick.net https://www.google.com https://googleads.g.doubleclick.net https://www.googletagmanager.com; worker-src 'self' blob:;"

# Cache static assets
[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.svg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.webp"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Our Team - Truly Painting</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#D4A59A", // rose gold as main color for primary CTAs
              secondary: "#2A2A2A", // deep charcoal/off-black as secondary color
              tertiary: "#1E3A8A", // deep blue for tertiary elements
              dark: "#1A1A1A", // dark color for dark mode sections
              light: "#F8F8F8", // light color for light sections
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      /* Custom rose gold gradient for headlines */
      .rose-gold-gradient {
        background: linear-gradient(
          to right,
          #d4a59a,
          /* Base rose gold */ #e8c3b9,
          /* Lighter rose gold */ #c69489,
          /* Deeper rose gold */ #d4a59a /* Back to base rose gold */
        );
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        text-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .hover-grow {
        transition: transform 0.2s ease;
      }
      .hover-grow:hover {
        transform: scale(1.02);
      }
    </style>
        <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-NBKXB3TF");
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      id="header"
      class="fixed w-full z-50 transition-all duration-300 py-4"
    >
      <div class="container mx-auto px-4 md:px-6">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <a href="index.html">
              <img
                src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
                alt="Truly Painting Logo"
                class="h-12 logo-transparent"
              />
              <img
                src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
                alt="Truly Painting Logo"
                class="h-12 logo-solid hidden"
              />
            </a>
          </div>

          <div class="hidden md:flex items-center space-x-8">
            <nav>
              <ul class="flex space-x-6">
                <li>
                  <a
                    href="services.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Services</a
                  >
                </li>

                <li>
                  <a
                    href="team.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Our Team</a
                  >
                </li>
                <li>
                  <a
                    href="visualizer.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Visualizer</a
                  >
                </li>
                <li>
                  <a
                    href="index.html#contact"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Contact</a
                  >
                </li>
              </ul>
            </nav>

            <a
              href="tel:5094617090"
              class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md font-medium transition"
            >
              <svg
                class="w-[18px] h-[18px] mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                />
              </svg>
              (*************
            </a>
          </div>

          <button class="md:hidden text-primary" onclick="toggleMobileMenu()">
            <svg
              class="w-7 h-7"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobileMenu"
        class="hidden md:hidden bg-white shadow-lg fixed top-0 left-0 right-0 z-50 mt-16 max-h-[calc(100vh-4rem)] overflow-y-auto"
      >
        <nav class="container mx-auto px-4 py-4">
          <ul class="space-y-4">
            <li>
              <a
                href="services.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>

            <li>
              <a
                href="team.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="visualizer.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Visualizer
              </a>
            </li>
            <li>
              <a
                href="index.html#contact"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Contact
              </a>
            </li>
            <li>
              <a
                href="tel:5094617090"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-medium justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                (*************
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <script>
      // Header scroll effect
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navLinks = document.querySelectorAll(".nav-link");
        const logoTransparent = document.querySelector(".logo-transparent");
        const logoSolid = document.querySelector(".logo-solid");

        if (window.scrollY > 20) {
          header.classList.remove("py-4");
          header.classList.add("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.remove("text-white");
            link.classList.add("text-gray-800");
          });
          logoTransparent.classList.add("hidden");
          logoSolid.classList.remove("hidden");
        } else {
          header.classList.add("py-4");
          header.classList.remove("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.add("text-white");
            link.classList.remove("text-gray-800");
          });
          logoTransparent.classList.remove("hidden");
          logoSolid.classList.add("hidden");
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const header = document.getElementById("header");

        // Adjust mobile menu position based on header height
        const headerHeight = header.offsetHeight;
        mobileMenu.style.marginTop = headerHeight + "px";

        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }
    </script>

    <!-- Hero Section -->
    <section class="relative h-[60vh] flex items-center">
      <div class="absolute inset-0">
        <img
          src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e7acff6b5c57046dbc9a9.webp"
          alt="Joseph & Machaela Weltzin - Truly Painting Team"
          class="w-full h-full object-cover md:object-[center_25%] transition-transform duration-500"
        />
        <div
          class="absolute inset-0 bg-gradient-to-b from-black/70 to-primary/80"
        ></div>
      </div>

      <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-4xl mx-auto text-center text-white">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">
            <span class="rose-gold-gradient"
              >Meet Our Team: Skilled Painters, Dedicated Professionals</span
            >
          </h1>
          <p class="hidden md:block text-xl opacity-90">
            The talented team behind Newport's most beautiful painting projects
          </p>
        </div>
      </div>
    </section>

    <!-- Team Introduction Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <p class="text-lg text-gray-700 mb-8">
            At Truly Painting, our greatest asset is our team. We are a group of
            experienced, skilled, and dedicated professionals who take immense
            pride in our work and are committed to providing an exceptional
            experience for every client. From our project managers to our expert
            painters, everyone at Truly Painting shares a passion for creating
            high-quality, beautiful painting projects that our Newport neighbors
            can enjoy for years to come.
          </p>
        </div>
      </div>
    </section>

    <!-- Leadership Team Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <h2
          class="text-3xl md:text-4xl font-bold text-center mb-16 text-primary"
        >
          Meet Our Leadership
        </h2>

        <div class="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          <!-- Joseph & Machaela Weltzin - Owners -->
          <div
            class="bg-white rounded-lg shadow-lg overflow-hidden hover-grow md:col-span-1"
          >
            <div class="h-64 bg-gray-200">
              <img
                src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e7acff6b5c57046dbc9a9.webp"
                alt="Joseph & Machaela Weltzin"
                class="w-full h-full object-cover"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-1">
                Joseph & Machaela Weltzin
              </h3>
              <p class="text-primary font-medium mb-4">Owners</p>
              <p class="text-gray-600 text-sm">
                A good contractor is hard to come by in this area, let alone an
                exceptional one. That's why Joe Weltzin and his family launched
                Truly Painting to offer painting & premium woodworking that
                grips attention. Their company has gained a reputation as a
                market leader, with a steady stream of happy, high-end clients
                that would say the same!
              </p>
            </div>
          </div>

          <!-- Michael Johnson - Lead Painter -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden hover-grow">
            <div class="h-64 bg-gray-200">
              <img
                src="https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457fb4903bf9edcf3d79.webp"
                alt="Michael Johnson"
                class="w-full h-full object-cover"
              />
            </div>
            <div class="p-6">
              <h3 class="text-xl font-bold text-gray-900 mb-1">
                Taylor Masteller
              </h3>
              <p class="text-primary font-medium mb-4">Lead Painter</p>
              <p class="text-gray-600 text-sm">
                Michael's extensive knowledge of painting techniques and
                materials helps clients make informed decisions. His expertise
                ensures we use the best products for Washington's unique
                climate.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Team Members Section - Archived
    This section has been commented out and archived.
    It contained profiles for team members including:
    - David Thompson (Senior Painter)
    - Emily Rodriguez (Project Manager)
    - Ryan Martinez (Color Specialist)
    - Jessica Kim (Customer Relations)
    -->

    <!-- What Makes Our Team Different Section -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <h2
            class="text-3xl md:text-4xl font-bold text-center mb-12 text-primary"
          >
            What Makes Our Team Different?
          </h2>

          <div class="grid md:grid-cols-2 gap-8">
            <!-- Left Column -->
            <div>
              <div class="mb-8">
                <div class="flex items-start mb-3">
                  <div
                    class="bg-primary text-white p-2 rounded-full mr-4 flex-shrink-0"
                  >
                    <svg
                      class="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">
                      True Professionals & Master Painters
                    </h3>
                    <p class="text-gray-700">
                      Our customers consistently praise the skill and artistry
                      of our painters, calling them "perfectionists," "skilled,"
                      and "artists." They are meticulous in their work, ensuring
                      every detail is "done right the first time."
                    </p>
                  </div>
                </div>
              </div>

              <div class="mb-8">
                <div class="flex items-start mb-3">
                  <div
                    class="bg-primary text-white p-2 rounded-full mr-4 flex-shrink-0"
                  >
                    <svg
                      class="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">
                      Exceptional Communicators
                    </h3>
                    <p class="text-gray-700">
                      You'll find our team, from Machaela and Joseph to every
                      crew member, to be responsive, informative, and genuinely
                      invested in your project's success. We believe in keeping
                      you updated every step of the way.
                    </p>
                  </div>
                </div>
              </div>

              <div class="mb-8">
                <div class="flex items-start mb-3">
                  <div
                    class="bg-primary text-white p-2 rounded-full mr-4 flex-shrink-0"
                  >
                    <svg
                      class="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">
                      Efficient & Hard-Working
                    </h3>
                    <p class="text-gray-700">
                      Our crews are known for their incredible speed and
                      efficiency, often completing projects "ahead of schedule"
                      without ever compromising on the quality Truly Painting is
                      known for.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column -->
            <div>
              <div class="mb-8">
                <div class="flex items-start mb-3">
                  <div
                    class="bg-primary text-white p-2 rounded-full mr-4 flex-shrink-0"
                  >
                    <svg
                      class="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">
                      Respectful & Courteous
                    </h3>
                    <p class="text-gray-700">
                      We treat your home and property with the utmost respect.
                      Our teams are praised for being "immaculate,"
                      "respectful," "kind and courteous," and for maintaining
                      clean and tidy worksites daily.
                    </p>
                  </div>
                </div>
              </div>

              <div class="mb-8">
                <div class="flex items-start mb-3">
                  <div
                    class="bg-primary text-white p-2 rounded-full mr-4 flex-shrink-0"
                  >
                    <svg
                      class="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">
                      Problem Solvers & Adaptable
                    </h3>
                    <p class="text-gray-700">
                      Painting in Washington presents unique challenges. Our
                      team is adept at problem-solving, navigating complex
                      surface preparation issues, addressing unforeseen
                      conditions, and working through difficult weather.
                    </p>
                  </div>
                </div>
              </div>

              <div class="mb-8">
                <div class="flex items-start mb-3">
                  <div
                    class="bg-primary text-white p-2 rounded-full mr-4 flex-shrink-0"
                  >
                    <svg
                      class="w-5 h-5"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      stroke-width="2"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                  </div>
                  <div>
                    <h3 class="text-lg font-bold text-gray-900 mb-2">
                      Client-Focused & Trustworthy
                    </h3>
                    <p class="text-gray-700">
                      Your satisfaction is our top priority. We listen to your
                      input, go "above and beyond," and stand by our work to
                      earn your trust. Many clients have told us they "made the
                      right choice" in choosing Truly Painting.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section (Archived) -->
    <section class="py-16 bg-dark text-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl md:text-4xl font-bold text-center mb-12">
          From Our Team to You
        </h2>

        <div
          class="bg-white/10 p-8 rounded-lg backdrop-blur-sm max-w-3xl mx-auto text-center"
        >
          <div class="flex justify-center mb-6">
            <svg
              class="w-12 h-12 text-primary"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
              <polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
          </div>
          <h3 class="text-2xl font-bold mb-4">
            Client Testimonials Coming Soon
          </h3>
          <p class="text-lg mb-6">
            We're in the process of collecting real testimonials from our
            satisfied customers. Check back soon to read about the experiences
            of homeowners who have trusted us with their painting projects.
          </p>
          <p class="text-xl font-medium mt-8">
            We look forward to putting our expertise and dedication to work for
            you!
          </p>
          <a
            href="index.html#contact"
            class="inline-block mt-6 px-8 py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-md transition"
          >
            Get Your FREE Estimate Today!
          </a>
        </div>
      </div>
    </section>

    <!-- Instagram Reels Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2
          class="text-3xl md:text-4xl font-bold text-center mb-6 text-primary"
        >
          Our Team in Action
        </h2>
        <p class="text-lg text-gray-700 text-center max-w-3xl mx-auto mb-12">
          Check out our latest Instagram reels showcasing our team's
          craftsmanship and painting process
        </p>

        <div class="relative">
          <button
            id="scrollLeft"
            class="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 p-2 rounded-full shadow-lg hover:bg-white transition-colors hidden md:block"
          >
            <svg
              class="w-6 h-6 text-primary"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>

          <div
            id="reelsContainer"
            class="flex overflow-x-auto gap-8 pb-6 hide-scrollbar"
            style="scroll-behavior: smooth"
          >
            <!-- Instagram Reels will be dynamically inserted here -->
          </div>

          <button
            id="scrollRight"
            class="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 p-2 rounded-full shadow-lg hover:bg-white transition-colors hidden md:block"
          >
            <svg
              class="w-6 h-6 text-primary"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
        </div>

        <div class="text-center mt-8">
          <a
            href="https://www.instagram.com/trulypainting/"
            target="_blank"
            class="inline-flex items-center text-primary hover:text-primary/80 font-medium transition-colors mr-4"
          >
            <svg
              class="w-5 h-5 mr-2"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
              <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
              <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
            </svg>
            Follow Truly Painting
          </a>
        </div>
      </div>
    </section>

    <style>
      .hide-scrollbar::-webkit-scrollbar {
        display: none;
      }

      .hide-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      .reel-card {
        flex: 0 0 auto;
        width: 320px;
        transition: all 0.3s ease;
        max-width: 90vw; /* Prevent overflow on small screens */
      }

      .reel-card:hover {
        transform: translateY(-5px);
      }

      /* Maintain aspect ratio for images */
      .reel-card .aspect-\[9\/16\] {
        padding-bottom: 178%; /* Maintain proper aspect ratio */
        height: 0;
        overflow: hidden;
        position: relative;
      }

      .reel-card img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      /* Instagram icon color */
      .reel-card .text-primary {
        color: #d4a59a;
      }

      @media (max-width: 640px) {
        .reel-card {
          width: 280px; /* Smaller size for mobile */
          max-width: 90vw; /* Prevent overflow on small screens */
        }
      }
    </style>

    <script>
      // Instagram Reels data
      const instagramReels = [
        {
          url: "https://www.instagram.com/trulypainting/p/DFOTND-vzUU/",
          imageUrl:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f9a0ad36873275d5b.webp",
          title: "Truly Painting Latest Work",
          account: "@trulypainting",
        },
        {
          url: "https://www.instagram.com/visionpainting23/reel/DE2ynYryLcd/",
          imageUrl:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f096f560eec651ec7.webp",
          title: "Vision Painting Project",
          account: "@visionpainting23",
        },
        {
          url: "https://www.instagram.com/visionpainting23/reel/C6Wyja7Jh0n/",
          imageUrl:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f9a0ad363e9275d5c.webp",
          title: "Professional Painting Process",
          account: "@visionpainting23",
        },
        {
          url: "https://www.instagram.com/visionpainting23/reel/C1BVjOUrgh0/",
          imageUrl:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457fb4903bf9edcf3d79.webp",
          title: "Painting Transformation",
          account: "@visionpainting23",
        },
        {
          url: "https://www.instagram.com/visionpainting23/reel/C6SLvYbvKDb/",
          imageUrl:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f096f56e0e2651ec6.webp",
          title: "Expert Painting Techniques",
          account: "@visionpainting23",
        },
        {
          url: "https://www.instagram.com/visionpainting23/reel/C05pJqeLVLt/",
          imageUrl:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457fb4903b5d4acf3d78.webp",
          title: "Beautiful Painting Results",
          account: "@visionpainting23",
        },
        {
          url: "https://www.instagram.com/visionpainting23/reel/CjtCDq5J_rO/",
          imageUrl:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f6c40216f46e42ba9.webp",
          title: "Painting Craftsmanship",
          account: "@visionpainting23",
        },
        {
          url: "https://www.instagram.com/visionpainting23/reel/CjGcgnwBoTB/",
          imageUrl:
            "https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/681e457f096f560eec651ec7.webp",
          title: "Quality Painting Work",
          account: "@visionpainting23",
        },
      ];

      // Initialize Instagram Reels
      document.addEventListener("DOMContentLoaded", () => {
        const reelsContainer = document.getElementById("reelsContainer");
        const scrollLeftBtn = document.getElementById("scrollLeft");
        const scrollRightBtn = document.getElementById("scrollRight");

        // Populate reels container
        instagramReels.forEach((reel) => {
          const reelCard = document.createElement("div");
          reelCard.className =
            "reel-card bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all";

          // Create a clickable card with image preview
          reelCard.innerHTML = `
            <a href="${reel.url}" target="_blank" class="block">
              <div class="aspect-[9/16] bg-gray-100 relative overflow-hidden">
                <img
                  src="${reel.imageUrl}"
                  alt="${reel.title}"
                  class="w-full h-full object-cover transition-transform hover:scale-105"
                />
                <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                  <div class="p-4 text-white">
                    <div class="flex items-center mb-2">
                      <svg class="w-5 h-5 mr-2 text-primary" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                        <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                        <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                      </svg>
                      <span class="text-sm font-medium">${reel.account}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="p-4 pb-6">
                <h3 class="font-medium text-gray-900">${reel.title}</h3>
                <p class="text-sm text-primary mt-1">View on Instagram</p>
              </div>
            </a>
          `;

          reelsContainer.appendChild(reelCard);
        });

        // Scroll functionality
        scrollLeftBtn.addEventListener("click", () => {
          reelsContainer.scrollBy({ left: -340, behavior: "smooth" });
        });

        scrollRightBtn.addEventListener("click", () => {
          reelsContainer.scrollBy({ left: 340, behavior: "smooth" });
        });

        // Show/hide scroll buttons based on scroll position
        reelsContainer.addEventListener("scroll", () => {
          // Show left button only if not at the start
          scrollLeftBtn.style.display =
            reelsContainer.scrollLeft > 0 ? "block" : "none";

          // Show right button only if not at the end
          const maxScrollLeft =
            reelsContainer.scrollWidth - reelsContainer.clientWidth;
          scrollRightBtn.style.display =
            reelsContainer.scrollLeft < maxScrollLeft - 10 ? "block" : "none";
        });

        // Initial check for scroll buttons
        setTimeout(() => {
          const maxScrollLeft =
            reelsContainer.scrollWidth - reelsContainer.clientWidth;
          scrollLeftBtn.style.display = "none"; // Initially hidden as we start at the beginning
          scrollRightBtn.style.display = maxScrollLeft > 10 ? "block" : "none";
        }, 500);
      });
    </script>

    <!-- Join Our Team CTA -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
          <h2 class="text-3xl md:text-4xl font-bold mb-6 text-primary">
            Join Our Team
          </h2>
          <p class="text-xl text-gray-600 mb-8">
            We're always looking for talented craftsmen and professionals who
            share our passion for exceptional painting craftsmanship and
            delivering outstanding customer service.
          </p>
          <a
            href="index.html#contact"
            class="inline-block bg-primary hover:bg-primary/90 text-white px-8 py-4 rounded-md font-semibold transition"
          >
            Contact Us About Opportunities
          </a>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-secondary text-white py-12">
      <div class="container mx-auto px-4 md:px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <img
              src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
              alt="Truly Painting Logo"
              class="h-14 mb-4"
            />
            <p class="text-gray-300 mb-4">
              Newport's professional painting services. Quality craftsmanship,
              innovative solutions, and our exclusive AI preview app.
            </p>
            <div class="flex space-x-4">
              <a
                href="https://www.facebook.com/profile.php?id=61557209638007"
                target="_blank"
                class="text-primary hover:text-primary/80 transition"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"
                  ></path>
                </svg>
              </a>
              <a
                href="https://www.instagram.com/trulypainting/"
                target="_blank"
                class="text-primary hover:text-primary/80 transition"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path
                    d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"
                  ></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
            <ul class="space-y-3">
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <a href="tel:5094617090" class="hover:text-primary transition"
                  >(*************</a
                >
              </li>
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="hover:text-primary transition"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-start">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary mt-1"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                  ></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <span>141 Elu Beach Rd, Newport, WA 99156</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Hours & Information</h3>
            <ul class="space-y-2 text-gray-300">
              <li>Monday-Friday: 8am-6pm</li>
              <li>Serving Newport & Surrounding Areas</li>
              <li>Fully Insured and Bonded</li>
              <li>Professional Painting Services</li>
              <li>Free AI Color Preview App</li>
            </ul>
          </div>
        </div>

        <div
          class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
        >
          <p>
            ©
            <script>
              document.write(new Date().getFullYear());
            </script>
            Truly Painting. All rights reserved.
          </p>
          <p class="mt-2">
            <a href="privacy.html" class="hover:text-primary transition"
              >Privacy Policy</a
            >
            |
            <a href="terms.html" class="hover:text-primary transition"
              >Terms of Service</a
            >
          </p>
        </div>
      </div>
    </footer>

    <script>
      // Header scroll effect
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navLinks = document.querySelectorAll(".nav-link");

        if (window.scrollY > 20) {
          header.classList.remove("py-4");
          header.classList.add("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.remove("text-white");
            link.classList.add("text-gray-800");
          });
        } else {
          header.classList.add("py-4");
          header.classList.remove("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.add("text-white");
            link.classList.remove("text-gray-800");
          });
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }
    </script>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-NBKXB3TF"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
  </body>
</html>

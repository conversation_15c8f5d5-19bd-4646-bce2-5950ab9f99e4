<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Thank You - Truly Painting</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#D4A59A", // rose gold as main color for primary CTAs
              secondary: "#2A2A2A", // deep charcoal/off-black as secondary color
              tertiary: "#1E3A8A", // deep blue for tertiary elements
              dark: "#1A1A1A", // dark color for dark mode sections
              light: "#F8F8F8", // light color for light sections
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-fadeIn {
        animation: fadeIn 0.5s ease-out forwards;
      }

      .hover-grow {
        transition: transform 0.2s ease;
      }
      .hover-grow:hover {
        transform: scale(1.02);
      }

      /* Custom rose gold gradient for headlines */
      .rose-gold-gradient {
        background: linear-gradient(
          to right,
          #d4a59a,
          /* Base rose gold */ #e8c3b9,
          /* Lighter rose gold */ #c69489,
          /* Deeper rose gold */ #d4a59a /* Back to base rose gold */
        );
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        text-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);
      }
    </style>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-NBKXB3TF");
    </script>
    <!-- End Google Tag Manager -->
  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      id="header"
      class="fixed w-full z-50 transition-all duration-300 py-4"
    >
      <div class="container mx-auto px-4 md:px-6">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <a href="index.html">
              <img
                src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
                alt="Truly Painting Logo"
                class="h-12 logo-transparent"
              />
              <img
                src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
                alt="Truly Painting Logo"
                class="h-12 logo-solid hidden"
              />
            </a>
          </div>

          <div class="hidden md:flex items-center space-x-8">
            <nav>
              <ul class="flex space-x-6">
                <li>
                  <a
                    href="services.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Services</a
                  >
                </li>

                <li>
                  <a
                    href="team.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Our Team</a
                  >
                </li>
                <li>
                  <a
                    href="visualizer.html"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Visualizer</a
                  >
                </li>
                <li>
                  <a
                    href="index.html#contact"
                    class="nav-link font-medium hover:text-primary transition text-white"
                    >Contact</a
                  >
                </li>
              </ul>
            </nav>

            <a
              href="tel:5094617090"
              class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md font-medium transition"
            >
              <svg
                class="w-[18px] h-[18px] mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                />
              </svg>
              (*************
            </a>
          </div>

          <button class="md:hidden text-white" onclick="toggleMobileMenu()">
            <svg
              class="w-7 h-7"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobileMenu"
        class="hidden md:hidden bg-white shadow-lg fixed top-0 left-0 right-0 z-50 mt-16 max-h-[calc(100vh-4rem)] overflow-y-auto"
      >
        <nav class="container mx-auto px-4 py-4">
          <ul class="space-y-4">
            <li>
              <a
                href="services.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>

            <li>
              <a
                href="team.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="visualizer.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Visualizer
              </a>
            </li>
            <li>
              <a
                href="index.html#contact"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Contact
              </a>
            </li>
            <li>
              <a
                href="tel:5094617090"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-medium justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                (*************
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <script>
      // Header scroll effect
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navLinks = document.querySelectorAll(".nav-link");
        const logoTransparent = document.querySelector(".logo-transparent");
        const logoSolid = document.querySelector(".logo-solid");

        if (window.scrollY > 20) {
          header.classList.remove("py-4");
          header.classList.add("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.remove("text-white");
            link.classList.add("text-gray-800");
          });
          logoTransparent.classList.add("hidden");
          logoSolid.classList.remove("hidden");
        } else {
          header.classList.add("py-4");
          header.classList.remove("bg-white", "shadow-md", "py-2");
          navLinks.forEach((link) => {
            link.classList.add("text-white");
            link.classList.remove("text-gray-800");
          });
          logoTransparent.classList.remove("hidden");
          logoSolid.classList.add("hidden");
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const header = document.getElementById("header");

        // Adjust mobile menu position based on header height
        const headerHeight = header.offsetHeight;
        mobileMenu.style.marginTop = headerHeight + "px";

        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }
    </script>

    <!-- Thank You Section -->
    <section class="pt-32 pb-16 bg-light">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center mb-12">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">
            <span class="rose-gold-gradient">Thank You!</span>
          </h1>
          <p class="text-xl text-gray-700 mb-8">
            We appreciate your interest in Truly Painting. Schedule your appointment below or call us at 
            <a href="tel:5094617090" class="text-primary font-semibold hover:underline">(*************</a>.
          </p>
        </div>

        <div class="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-6 md:p-8">
          <h2 class="text-2xl font-bold text-center mb-6">Book Your Appointment</h2>
          
          <!-- Booking Widget -->
          <div class="booking-widget-container">
            <iframe src="https://api.leadconnectorhq.com/widget/booking/ezw8o4WNexYXFaBxKHQc" style="width: 100%;border:none;overflow: hidden;" scrolling="no" id="ezw8o4WNexYXFaBxKHQc_1747537297230"></iframe>
            <script src="https://link.msgsndr.com/js/form_embed.js" type="text/javascript"></script>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-secondary text-white py-12">
      <div class="container mx-auto px-4 md:px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <img
              src="https://storage.googleapis.com/msgsndr/7mewc5RIQe9kefPF1P31/media/67aee2ab2ba94e912ea18552.png"
              alt="Truly Painting Logo"
              class="h-14 mb-4"
            />
            <p class="text-gray-300 mb-4">
              Newport's professional painting services. Quality craftsmanship,
              innovative solutions, and our exclusive AI preview app.
            </p>
            <div class="flex space-x-4">
              <a
                href="https://www.facebook.com/profile.php?id=61557209638007"
                target="_blank"
                class="text-primary hover:text-primary/80 transition"
              >
                <span class="sr-only">Facebook</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"
                  ></path>
                </svg>
              </a>
              <a
                href="https://www.instagram.com/trulypainting/"
                target="_blank"
                class="text-primary hover:text-primary/80 transition"
              >
                <span class="sr-only">Instagram</span>
                <svg
                  class="w-6 h-6"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path
                    d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"
                  ></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
            <ul class="space-y-3">
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <a href="tel:5094617090" class="hover:text-primary transition"
                  >(*************</a
                >
              </li>
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="hover:text-primary transition"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-start">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-primary mt-1"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"
                  ></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <span>141 Elu Beach Rd, Newport, WA 99156</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Hours & Information</h3>
            <ul class="space-y-2 text-gray-300">
              <li>Monday-Friday: 8am-6pm</li>
              <li>Serving Newport & Surrounding Areas</li>
              <li>Fully Insured and Bonded</li>
              <li>Professional Painting Services</li>
              <li>Free AI Color Preview App</li>
            </ul>
          </div>
        </div>

        <div
          class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
        >
          <p>
            ©
            <script>
              document.write(new Date().getFullYear());
            </script>
            Truly Painting. All rights reserved.
          </p>
          <p class="mt-2">
            <a href="privacy.html" class="hover:text-primary transition"
              >Privacy Policy</a
            >
            |
            <a href="terms.html" class="hover:text-primary transition"
              >Terms of Service</a
            >
          </p>
        </div>
      </div>
    </footer>
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe
        src="https://www.googletagmanager.com/ns.html?id=GTM-NBKXB3TF"
        height="0"
        width="0"
        style="display: none; visibility: hidden"
      ></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->

    <!-- Chat Widget  -->
    <div
      data-chat-widget
      data-widget-id="6802ed1930666bcb1727d04c"
      data-location-id="7mewc5RIQe9kefPF1P31"
    ></div>
    <script
      src="https://widgets.leadconnectorhq.com/loader.js"
      data-resources-url="https://widgets.leadconnectorhq.com/chat-widget/loader.js"
      data-widget-id="6802ed1930666bcb1727d04c"
    ></script>
    <!-- End Chat Widget  -->
  </body>
</html>
